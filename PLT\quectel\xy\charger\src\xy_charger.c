#include <stdint.h>
#include "osa.h"
#include "UART.h"
#include "ql_gpio.h"
#include "ql_adc.h"
#include "sc8906x_charger.h"
#include "xy_charger.h"

/* 日志输出宏定义 */
#define CHARGER_LOG_INFO(fmt, ...) RTI_LOG("[CHARGER/INFO] " fmt "\r\n", ##__VA_ARGS__)
#define CHARGER_LOG_ERROR(fmt, ...) RTI_LOG("[CHARGER/ERROR] " fmt "\r\n", ##__VA_ARGS__)

/* 硬件配置 */
#define CHARGER_INT_PIN GPIO_38
#define CHARGER_CE_PIN GPIO_53

/* 电池电量相关配置 */
#define BATTERY_ADC_CHANNEL QL_ADC_CHANNEL_0 /**< 电池电压检测ADC通道 */
#define BATTERY_VOLTAGE_MIN 3300             /**< 电池最低电压 (mV) - 对应0% */
#define BATTERY_VOLTAGE_MAX 4200             /**< 电池最高电压 (mV) - 对应100% */

/* 分压电路配置 */
#define DIVIDER_RATIO_NUM 1390 /**< 分压比分子 (1M + 390K) */
#define DIVIDER_RATIO_DEN 390  /**< 分压比分母 (390K) */

/* RTOS任务参数配置 */
#define CHARGER_TASK_STACK_SIZE 2048     /**< 充电器任务栈大小 */
#define CHARGER_TASK_PRIORITY 15         /**< 充电器任务优先级 */
#define CHARGER_TASK_NAME "charger_task" /**< 充电器任务名称 */

/* 状态寄存器数组索引枚举 */
typedef enum
{
    STATUS_REG08_INDEX = 0, /**< REG08在状态寄存器数组中的索引 */
    STATUS_REG09_INDEX = 1, /**< REG09在状态寄存器数组中的索引 */
    STATUS_REG0A_INDEX = 2, /**< REG0A在状态寄存器数组中的索引 */
    STATUS_REGS_COUNT       /**< 状态寄存器总数 */
} status_reg_index_t;

/* 静态分配栈 */
static OSSemaRef s_charger_sem = NULL;
static volatile xy_charge_status_t g_charger_status = XY_CHARGE_STATUS_UNKNOWN;

static uint8_t s_charger_stack[CHARGER_TASK_STACK_SIZE];
static OSATaskRef s_charger_task = NULL;

/* 充电状态和VBUS类型的字符串映射 */
static const char *chrg_status_str[] = {"IDLE", "PRECHG", "FASTCHG", "CHGDONE"};
static const char *vbus_type_str[] = {"NONE", "USB", "RSVD", "ADAPTER", "RSVD", "RSVD", "RSVD", "OTG"};

xy_charge_status_t xy_charger_get_status(void)
{
    return g_charger_status;
}

int xy_charger_get_battery_level(void)
{
    ql_errcode_adc_e ret;
    uint16_t adc_volt;
    uint32_t bat_volt;
    int level;

    /* 获取ADC测量电压 */
    ret = ql_adc_get_volt(BATTERY_ADC_CHANNEL, &adc_volt);
    if (ret != QL_ADC_SUCCESS)
    {
        CHARGER_LOG_ERROR("Failed to read battery voltage: %d", ret);
        return -1;
    }

    /* 计算实际电池电压：adc_volt * 1390 / 390 */
    bat_volt = (uint32_t)adc_volt * DIVIDER_RATIO_NUM / DIVIDER_RATIO_DEN;

    /* 计算电量等级 */
    if (bat_volt <= BATTERY_VOLTAGE_MIN)
    {
        level = 0;
    }
    else if (bat_volt >= BATTERY_VOLTAGE_MAX)
    {
        level = 100;
    }
    else
    {
        level = (bat_volt - BATTERY_VOLTAGE_MIN) * 100 /
                (BATTERY_VOLTAGE_MAX - BATTERY_VOLTAGE_MIN);
    }

    // todo: 测试用
    CHARGER_LOG_INFO("ADC: %d mV, Battery: %lu mV, Level: %d%%",
                     adc_volt, bat_volt, level);

    return level;
}

/* GPIO初始化 */
static int xy_charger_gpio_init(void)
{
    uint16_t ce_pin_num;
    int ret;

    /* 初始化充电使能引脚 */
    ret = ql_gpio_get_pin_num(CHARGER_CE_PIN, &ce_pin_num);
    if (ret != QL_GPIO_SUCCESS)
    {
        CHARGER_LOG_ERROR("Get CE pin num fail, GPIO%d, ret=%d", CHARGER_CE_PIN, ret);
        return -1;
    }

    ret = ql_pin_set_gpio(ce_pin_num);
    if (ret != QL_GPIO_SUCCESS)
    {
        CHARGER_LOG_ERROR("Set CE pin gpio func fail, PIN%d, ret=%d", ce_pin_num, ret);
        return -1;
    }

    ret = ql_gpio_init(CHARGER_CE_PIN, GPIO_OUTPUT, PULL_NONE, GPIO_LOW);
    if (ret != QL_GPIO_SUCCESS)
    {
        CHARGER_LOG_ERROR("CHARGER_CE_PIN init failed: GPIO%d, ret=%d", CHARGER_CE_PIN, ret);
        return -1;
    }

    CHARGER_LOG_INFO("CHARGER_CE_PIN initialized: GPIO%d (PIN%d)", CHARGER_CE_PIN, ce_pin_num);
    return 0;
}

static void charger_int_handler(void *ctx)
{
    OSASemaphoreRelease(s_charger_sem);
}

static int setup_charger_interrupt(void)
{
    int ret;
    uint16_t int_pin_num;

    /* 获取中断GPIO对应的PIN号 */
    ret = ql_gpio_get_pin_num(CHARGER_INT_PIN, &int_pin_num);
    if (ret != QL_GPIO_SUCCESS)
    {
        CHARGER_LOG_ERROR("Get INT pin num fail, GPIO%d, ret=%d", CHARGER_INT_PIN, ret);
        return ret;
    }

    /* 设置PIN为GPIO功能 */
    ret = ql_pin_set_gpio(int_pin_num);
    if (ret != QL_GPIO_SUCCESS)
    {
        CHARGER_LOG_ERROR("Set INT pin gpio func fail, PIN%d, ret=%d", int_pin_num, ret);
        return ret;
    }

    CHARGER_LOG_INFO("CHARGER_INT_PIN configured: GPIO%d (PIN%d)", CHARGER_INT_PIN, int_pin_num);

    /* 配置中断 */
    ql_gpio_int_cfg_t int_cfg = {
        .gpio_num = CHARGER_INT_PIN,
        .gpio_pull = PULL_NONE,
        .int_type = INT_EDGES_FALLING,
        .wakeup_en = 0,
        .db_en = INT_DEBOUNCE_DISABLE,
        .int_callback = charger_int_handler,
        .cb_ctx = NULL};

    ret = ql_int_register_ex(&int_cfg);
    if (ret != QL_GPIO_SUCCESS)
    {
        CHARGER_LOG_ERROR("Register charger interrupt failed: %d", ret);
        return ret;
    }

    ret = ql_int_enable(CHARGER_INT_PIN);
    if (ret != QL_GPIO_SUCCESS)
    {
        CHARGER_LOG_ERROR("Enable charger interrupt failed: %d", ret);
        ql_int_unregister(CHARGER_INT_PIN);
        return ret;
    }

    return 0;
}

static xy_charge_status_t parse_charge_status(uint8_t reg08)
{
    uint8_t chrg_stat;
    chrg_stat = (reg08 & REG08_CHRG_STAT_MASK) >> REG08_CHRG_STAT_SHIFT;

    switch (chrg_stat)
    {
    case REG08_CHRG_STAT_IDLE:
        return XY_CHARGE_STATUS_IDLE;

    case REG08_CHRG_STAT_PRECHG:
        return XY_CHARGE_STATUS_PRECHG;

    case REG08_CHRG_STAT_FASTCHG:
        return XY_CHARGE_STATUS_FASTCHG;

    case REG08_CHRG_STAT_CHGDONE:
        return XY_CHARGE_STATUS_CHGDONE;

    default:
        return XY_CHARGE_STATUS_UNKNOWN;
    }
}

static void print_status_regs(const uint8_t *status_regs)
{
    uint8_t reg08 = status_regs[STATUS_REG08_INDEX];
    uint8_t reg09 = status_regs[STATUS_REG09_INDEX];
    uint8_t reg0a = status_regs[STATUS_REG0A_INDEX];

    /* REG08 状态解析 */
    uint8_t vbus_stat = (reg08 & REG08_VBUS_STAT_MASK) >> REG08_VBUS_STAT_SHIFT;
    uint8_t chrg_stat = (reg08 & REG08_CHRG_STAT_MASK) >> REG08_CHRG_STAT_SHIFT;
    uint8_t pg_stat = (reg08 & REG08_PG_STAT_MASK) >> REG08_PG_STAT_SHIFT;
    uint8_t therm_stat = (reg08 & REG08_THERM_STAT_MASK) >> REG08_THERM_STAT_SHIFT;
    uint8_t vsys_stat = (reg08 & REG08_VSYS_STAT_MASK) >> REG08_VSYS_STAT_SHIFT;

    /* REG09 故障状态解析 */
    uint8_t wdt_fault = (reg09 & REG09_FAULT_WDT_MASK) >> REG09_FAULT_WDT_SHIFT;
    uint8_t boost_fault = (reg09 & REG09_FAULT_BOOST_MASK) >> REG09_FAULT_BOOST_SHIFT;
    uint8_t chrg_fault = (reg09 & REG09_FAULT_CHRG_MASK) >> REG09_FAULT_CHRG_SHIFT;
    uint8_t bat_fault = (reg09 & REG09_FAULT_BAT_MASK) >> REG09_FAULT_BAT_SHIFT;
    uint8_t ntc_fault = (reg09 & REG09_FAULT_NTC_MASK) >> REG09_FAULT_NTC_SHIFT;

    /* REG0A 状态解析 */
    uint8_t vbus_gd = (reg0a & REG0A_VBUS_GD_MASK) >> REG0A_VBUS_GD_SHIFT;
    uint8_t vindpm_stat = (reg0a & REG0A_VINDPM_STAT_MASK) >> REG0A_VINDPM_STAT_SHIFT;
    uint8_t iindpm_stat = (reg0a & REG0A_IINDPM_STAT_MASK) >> REG0A_IINDPM_STAT_SHIFT;
    uint8_t topoff_active = (reg0a & REG0A_TOPOFF_ACTIVE_MASK) >> REG0A_TOPOFF_ACTIVE_SHIFT;
    uint8_t acov_stat = (reg0a & REG0A_ACOV_STAT_MASK) >> REG0A_ACOV_STAT_SHIFT;

    CHARGER_LOG_INFO("=== Charger Status Registers ===");
    CHARGER_LOG_INFO("REG08=0x%02X: VBUS=%d, CHRG=%d, PG=%d, THERM=%d, VSYS=%d",
                     reg08, vbus_stat, chrg_stat, pg_stat, therm_stat, vsys_stat);
    CHARGER_LOG_INFO("REG09=0x%02X: WDT=%d, BOOST=%d, CHRG_FLT=%d, BAT=%d, NTC=%d",
                     reg09, wdt_fault, boost_fault, chrg_fault, bat_fault, ntc_fault);
    CHARGER_LOG_INFO("REG0A=0x%02X: VBUS_GD=%d, VINDPM=%d, IINDPM=%d, TOPOFF=%d, ACOV=%d",
                     reg0a, vbus_gd, vindpm_stat, iindpm_stat, topoff_active, acov_stat);

    /* 显示易读的充电状态和VBUS类型 */
    CHARGER_LOG_INFO("Charge Status: %s, VBUS Type: %s",
                     chrg_status_str[chrg_stat], vbus_type_str[vbus_stat]);
}

static void update_charger_status(void)
{
    int ret;
    uint8_t reg09_val2;
    xy_charge_status_t status;
    uint8_t status_regs[STATUS_REGS_COUNT];

    ret = hal_get_charger_regs(SC8960X_REG_08, status_regs, STATUS_REGS_COUNT);
    if (ret < 0)
    {
        CHARGER_LOG_ERROR("Failed to read status registers: %d", ret);
        return;
    }

    ret = hal_get_charger_regs(SC8960X_REG_09, &reg09_val2, 1);
    if (ret < 0)
    {
        CHARGER_LOG_ERROR("Failed to read REG09 second time: %d", ret);
        return;
    }

    status_regs[STATUS_REG09_INDEX] = reg09_val2;

    print_status_regs(status_regs);

    status = parse_charge_status(status_regs[STATUS_REG08_INDEX]);
    if (status != g_charger_status)
    {
        CHARGER_LOG_INFO("Charge status changed: %d -> %d", g_charger_status, status);
        g_charger_status = status;
    }
}

static void charger_task_entry(void *arg)
{
    CHARGER_LOG_INFO("Charger task start");

    while (1)
    {
        int rc = OSASemaphoreAcquire(s_charger_sem, OS_SUSPEND);
        if (rc == OS_SUCCESS)
        {
            update_charger_status();
        }
    }
}

/* 初始化 */
int xy_charger_init(void)
{
    int ret;

    if (s_charger_sem)
    {
        CHARGER_LOG_INFO("Already initialized");
        return 0;
    }

    CHARGER_LOG_INFO("XY Charger module init");

    /* 初始化充电器GPIO */
    ret = xy_charger_gpio_init();
    if (ret != 0)
    {
        CHARGER_LOG_ERROR("Charger GPIO init failed: ret=%d", ret);
        goto fail;
    }

    /* 初始化底层驱动 */
    ret = sc8960x_dev_init();
    if (ret != 0)
    {
        CHARGER_LOG_ERROR("sc8960x_dev_init failed: %d", ret);
        goto fail;
    }

    /* 读取初始充电状态 */
    update_charger_status();

    /* 创建信号量 */
    ret = OSASemaphoreCreate(&s_charger_sem, 0, OS_FIFO);
    if (ret != OS_SUCCESS)
    {
        CHARGER_LOG_ERROR("Create semaphore fail");
        goto fail;
    }

    /* 创建充电任务 */
    ret = OSATaskCreate(&s_charger_task, s_charger_stack, sizeof(s_charger_stack),
                        CHARGER_TASK_PRIORITY, CHARGER_TASK_NAME,
                        charger_task_entry, NULL);
    if (ret != OS_SUCCESS)
    {
        CHARGER_LOG_ERROR("Create charger task fail");
        goto fail;
    }

    /* 配置中断引脚 */
    ret = setup_charger_interrupt();
    if (ret != 0)
    {
        CHARGER_LOG_ERROR("setup_charger_interrupt failed: %d", ret);
        goto fail;
    }

    CHARGER_LOG_INFO("XY Charger initialized successfully");
    return 0;

fail:
    if (s_charger_task)
    {
        OSATaskDelete(s_charger_task);
        s_charger_task = NULL;
    }

    if (s_charger_sem)
    {
        OSASemaphoreDelete(s_charger_sem);
        s_charger_sem = NULL;
    }

    g_charger_status = XY_CHARGE_STATUS_UNKNOWN;
    return ret;
}
