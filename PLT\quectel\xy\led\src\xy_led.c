#include <stdint.h>
#include <stdbool.h>
#include "osa.h"
#include "ql_wifi.h"
#include "xy_led_priv.h"
#include "xy_charger.h"
#include "uapapi.h"

/* LED 配置表 - 顺序对应 led_idx_e 枚举 */
static led_config_t s_led_cfgs[LED_COUNT] = {
    [LED_IDX_NET_4G_RED] = {.gpio_pin = GPIO_28, .name = "4G_Red", .status = LED_OFF},
    [LED_IDX_NET_4G_GREEN] = {.gpio_pin = GPIO_20, .name = "4G_Green", .status = LED_OFF},
    [LED_IDX_NET_5G_RED] = {.gpio_pin = GPIO_26, .name = "5G_Red", .status = LED_OFF},
    [LED_IDX_NET_5G_GREEN] = {.gpio_pin = GPIO_27, .name = "5G_Green", .status = LED_OFF},
    [LED_IDX_WIFI_GREEN] = {.gpio_pin = GPIO_19, .name = "WiFi_Green", .status = LED_OFF},
    [LED_IDX_CHARGER_RED] = {.gpio_pin = GPIO_25, .name = "Charger_Red", .status = LED_OFF},
    [LED_IDX_CHARGER_GREEN] = {.gpio_pin = GPIO_8, .name = "Charger_Green", .status = LED_OFF},
};

static OSMsgQRef s_evt_q = NULL;
static volatile bool s_exit_flag = false; /* 共享退出标志 */

/* 静态分配栈 */
static uint8_t s_led_stack[LED_TASK_STACK_SIZE];
static OSATaskRef s_led_task = NULL;

static uint8_t s_status_stack[STATUS_TASK_STACK_SIZE];
static OSATaskRef s_status_task = NULL;

static led_net_status_e check_network_status(void)
{
    /* TODO: 替换为实际平台API */
    return NET_STATUS_NO_REG; /* 默认值 */
}

static led_wifi_status_e check_wifi_status(void)
{
    /* 获取WiFi热点状态 */
    uap_config_param wifi_info = {0};

    /* 先尝试获取WiFi配置信息，如果失败说明WiFi可能未初始化 */
    if (ql_get_wifiinfo(&wifi_info) != 0)
    {
        LED_LOG_ERROR("Failed to get wifi info, assume WiFi is OFF");
        return WIFI_STATUS_OFF;
    }

    unsigned char uap_status = UAP_STATUS();

    if (uap_status == UAP_BSS_START)
    {
        LED_LOG_INFO("WiFi status: ON (BSS started)");
        return WIFI_STATUS_ON;
    }
    else
    {
        LED_LOG_INFO("WiFi status: OFF (BSS stopped, status=%d)", uap_status);
        return WIFI_STATUS_OFF;
    }
}

static led_charger_status_e check_charger_status(void)
{
    int bat_level = xy_charger_get_battery_level();
    if (bat_level < 0)
    {
        LED_LOG_ERROR("Failed to get battery level");
        return CHARGER_STATUS_NORMAL_BATTERY;
    }

    xy_charge_status_t charge_status = xy_charger_get_status();
    bool is_charging = (charge_status == XY_CHARGE_STATUS_PRECHG ||
                        charge_status == XY_CHARGE_STATUS_FASTCHG);

    if (bat_level < LOW_BATTERY_THRESHOLD)
    {
        return is_charging ? CHARGER_STATUS_LOW_BATTERY_CHARGING
                           : CHARGER_STATUS_LOW_BATTERY;
    }
    return is_charging ? CHARGER_STATUS_NORMAL_BATTERY_CHARGING
                       : CHARGER_STATUS_NORMAL_BATTERY;
}

/* GPIO初始化 */
static int xy_led_gpio_init(void)
{
    int i;

    for (i = 0; i < LED_COUNT; ++i)
    {
        uint16_t pin_num;
        int ret;

        ret = ql_gpio_get_pin_num(s_led_cfgs[i].gpio_pin, &pin_num);
        if (ret != QL_GPIO_SUCCESS)
        {
            LED_LOG_ERROR("Get pin num fail for %s, GPIO%d, ret=%d",
                          s_led_cfgs[i].name, s_led_cfgs[i].gpio_pin, ret);
            return -1;
        }

        ret = ql_pin_set_gpio(pin_num);
        if (ret != QL_GPIO_SUCCESS)
        {
            LED_LOG_ERROR("Set pin gpio func fail for %s, PIN%d, ret=%d",
                          s_led_cfgs[i].name, pin_num, ret);
            return -1;
        }

        ret = ql_gpio_init(s_led_cfgs[i].gpio_pin, GPIO_OUTPUT, PULL_NONE, GPIO_LOW);
        if (ret != QL_GPIO_SUCCESS)
        {
            LED_LOG_ERROR("Init %s fail, GPIO%d, ret=%d",
                          s_led_cfgs[i].name, s_led_cfgs[i].gpio_pin, ret);
            return -1;
        }

        LED_LOG_INFO("%s on GPIO%d (PIN%d)", s_led_cfgs[i].name, s_led_cfgs[i].gpio_pin, pin_num);
    }

    return 0;
}

static void apply_net_state(led_net_status_e state)
{
    LED_LOG_INFO("Network status changed to: %d", state);

    switch (state)
    {
    case NET_STATUS_NO_SIM: /* 不存在SIM卡或PIN码未解锁：4G和5G红灯都常亮 */
        LED_LOG_INFO("NO SIM: 4G/5G red LEDs ON");
        s_led_cfgs[LED_IDX_NET_4G_RED].status = LED_ON;
        s_led_cfgs[LED_IDX_NET_4G_GREEN].status = LED_OFF;
        s_led_cfgs[LED_IDX_NET_5G_RED].status = LED_ON;
        s_led_cfgs[LED_IDX_NET_5G_GREEN].status = LED_OFF;
        break;

    case NET_STATUS_NO_REG: /* 存在SIM卡但未注册：4G和5G红灯都闪烁 */
        LED_LOG_INFO("NO REG: 4G/5G red LEDs BLINK");
        s_led_cfgs[LED_IDX_NET_4G_RED].status = LED_BLINK_1HZ;
        s_led_cfgs[LED_IDX_NET_4G_GREEN].status = LED_OFF;
        s_led_cfgs[LED_IDX_NET_5G_RED].status = LED_BLINK_1HZ;
        s_led_cfgs[LED_IDX_NET_5G_GREEN].status = LED_OFF;
        break;

    case NET_STATUS_4G_REG_GOOD: /* 4G注册且信号好：4G绿灯常亮，5G全灭 */
        LED_LOG_INFO("4G GOOD: 4G green LED ON");
        s_led_cfgs[LED_IDX_NET_4G_RED].status = LED_OFF;
        s_led_cfgs[LED_IDX_NET_4G_GREEN].status = LED_ON;
        s_led_cfgs[LED_IDX_NET_5G_RED].status = LED_OFF;
        s_led_cfgs[LED_IDX_NET_5G_GREEN].status = LED_OFF;
        break;

    case NET_STATUS_4G_REG_POOR: /* 4G注册但信号差：4G绿灯闪烁，5G全灭 */
        LED_LOG_INFO("4G POOR: 4G green LED BLINK");
        s_led_cfgs[LED_IDX_NET_4G_RED].status = LED_OFF;
        s_led_cfgs[LED_IDX_NET_4G_GREEN].status = LED_BLINK_1HZ;
        s_led_cfgs[LED_IDX_NET_5G_RED].status = LED_OFF;
        s_led_cfgs[LED_IDX_NET_5G_GREEN].status = LED_OFF;
        break;

    case NET_STATUS_5G_REG_GOOD: /* 5G注册且信号好：5G绿灯常亮，4G全灭 */
        LED_LOG_INFO("5G GOOD: 5G green LED ON");
        s_led_cfgs[LED_IDX_NET_4G_RED].status = LED_OFF;
        s_led_cfgs[LED_IDX_NET_4G_GREEN].status = LED_OFF;
        s_led_cfgs[LED_IDX_NET_5G_RED].status = LED_OFF;
        s_led_cfgs[LED_IDX_NET_5G_GREEN].status = LED_ON;
        break;

    case NET_STATUS_5G_REG_POOR: /* 5G注册但信号差：5G绿灯闪烁，4G全灭 */
        LED_LOG_INFO("5G POOR: 5G green LED BLINK");
        s_led_cfgs[LED_IDX_NET_4G_RED].status = LED_OFF;
        s_led_cfgs[LED_IDX_NET_4G_GREEN].status = LED_OFF;
        s_led_cfgs[LED_IDX_NET_5G_RED].status = LED_OFF;
        s_led_cfgs[LED_IDX_NET_5G_GREEN].status = LED_BLINK_1HZ;
        break;

    default:
        LED_LOG_ERROR("Unknown network status: %d", state);
        break;
    }
}

static void apply_wifi_state(led_wifi_status_e state)
{
    LED_LOG_INFO("WiFi status changed to: %s", (state == WIFI_STATUS_ON) ? "ON" : "OFF");
    s_led_cfgs[LED_IDX_WIFI_GREEN].status = (state == WIFI_STATUS_ON) ? LED_ON : LED_OFF;
}

static void apply_charger_state(led_charger_status_e state)
{
    LED_LOG_INFO("Charger status changed to: %d", state);

    switch (state)
    {
    case CHARGER_STATUS_LOW_BATTERY: /* 电量<20%：红灯亮，绿灯灭 */
        LED_LOG_INFO("LOW BAT: Red LED ON");
        s_led_cfgs[LED_IDX_CHARGER_RED].status = LED_ON;
        s_led_cfgs[LED_IDX_CHARGER_GREEN].status = LED_OFF;
        break;

    case CHARGER_STATUS_LOW_BATTERY_CHARGING: /* 电量<20%且充电中：红灯闪烁，绿灯灭 */
        LED_LOG_INFO("LOW BAT CHARGING: Red LED BLINK");
        s_led_cfgs[LED_IDX_CHARGER_RED].status = LED_BLINK_1HZ;
        s_led_cfgs[LED_IDX_CHARGER_GREEN].status = LED_OFF;
        break;

    case CHARGER_STATUS_NORMAL_BATTERY: /* 电量≥20%或充满电：红灯灭，绿灯亮 */
        LED_LOG_INFO("NORMAL BAT: Green LED ON");
        s_led_cfgs[LED_IDX_CHARGER_RED].status = LED_OFF;
        s_led_cfgs[LED_IDX_CHARGER_GREEN].status = LED_ON;
        break;

    case CHARGER_STATUS_NORMAL_BATTERY_CHARGING: /* 电量≥20%且充电中：红灯灭，绿灯闪烁 */
        LED_LOG_INFO("NORMAL BAT CHARGING: Green LED BLINK");
        s_led_cfgs[LED_IDX_CHARGER_RED].status = LED_OFF;
        s_led_cfgs[LED_IDX_CHARGER_GREEN].status = LED_BLINK_1HZ;
        break;

    default:
        LED_LOG_ERROR("Unknown charger status: %d", state);
        break;
    }
}

static void led_apply_level(unsigned idx, int blink_on)
{
    static int last_gpio_state[LED_COUNT] = {
        GPIO_LOW, GPIO_LOW, GPIO_LOW,
        GPIO_LOW, GPIO_LOW, GPIO_LOW,
        GPIO_LOW};

    int new_state;

    switch (s_led_cfgs[idx].status)
    {
    case LED_OFF:
        new_state = GPIO_LOW;
        break;

    case LED_ON:
        new_state = GPIO_HIGH;
        break;

    case LED_BLINK_1HZ:
        new_state = blink_on ? GPIO_HIGH : GPIO_LOW;
        break;
    }

    if (new_state != last_gpio_state[idx])
    {
        ql_gpio_set_level(s_led_cfgs[idx].gpio_pin, new_state);
        last_gpio_state[idx] = new_state;
    }
}

/* LED 任务 */
static void led_task_entry(void *arg)
{
    LED_LOG_INFO("LED control task start");

    const uint32_t period = BLINK_1HZ_PERIOD_TICK;
    int blink_on = 0;
    int i;
    uint32_t last_toggle_tick = OSAGetTicks();

    while (!s_exit_flag) /* 检查退出标志 */
    {
        uint32_t elapsed = OSAGetTicks() - last_toggle_tick;
        uint32_t to_next = (elapsed >= period) ? 0 : (period - elapsed);

        led_event_msg_t msg;
        int rc = OSAMsgQRecv(s_evt_q, (UINT8 *)&msg, sizeof(msg), to_next);

        if (rc == OS_SUCCESS)
        {
            do
            {
                switch (msg.event_type)
                {
                case LED_EVENT_NET_STATUS_CHANGED:
                    apply_net_state(msg.data.net_status);
                    break;

                case LED_EVENT_WIFI_STATUS_CHANGED:
                    apply_wifi_state(msg.data.wifi_status);
                    break;

                case LED_EVENT_CHARGER_STATUS_CHANGED:
                    apply_charger_state(msg.data.charger_status);
                    break;

                default:
                    LED_LOG_ERROR("Unknown event type: %d", msg.event_type);
                    break;
                }
            } while (OSAMsgQRecv(s_evt_q, (UINT8 *)&msg, sizeof(msg), OS_NO_SUSPEND) == OS_SUCCESS);
        }
        else
        {
            /* 闪烁周期到达 */
            blink_on = !blink_on;
            last_toggle_tick = OSAGetTicks();
        }

        for (i = 0; i < LED_COUNT; i++)
        {
            led_apply_level(i, blink_on);
        }
    }

    /* 退出前关闭所有LED */
    LED_LOG_INFO("LED task exit, turn off all LEDs");
    for (i = 0; i < LED_COUNT; i++)
    {
        s_led_cfgs[i].status = LED_OFF;
        led_apply_level(i, 0);
    }

    LED_LOG_INFO("LED control task exit");
    s_led_task = NULL;
    OSATaskDelete(NULL);
}

/* 状态轮询任务 */
static void led_status_task_entry(void *arg)
{
    LED_LOG_INFO("Status task start");

    led_net_status_e last_net = (led_net_status_e)-1;
    led_wifi_status_e last_wifi = (led_wifi_status_e)-1;
    led_charger_status_e last_charger = (led_charger_status_e)-1;

    led_net_status_e net;
    led_wifi_status_e wifi;
    led_charger_status_e charger;
    led_event_msg_t msg;

    while (!s_exit_flag) /* 检查退出标志 */
    {
        net = check_network_status();
        wifi = check_wifi_status();
        charger = check_charger_status();

        if (net != last_net)
        {
            LED_LOG_INFO("Sending network status event: %d -> %d", last_net, net);
            msg.event_type = LED_EVENT_NET_STATUS_CHANGED;
            msg.data.net_status = net;

            if (OSAMsgQSend(s_evt_q, sizeof(led_event_msg_t), (UINT8 *)&msg,
                            OS_NO_SUSPEND) != OS_SUCCESS)
            {
                LED_LOG_ERROR("Failed to send network status event");
            }

            last_net = net;
        }

        if (wifi != last_wifi)
        {
            LED_LOG_INFO("Sending WiFi status event: %d -> %d", last_wifi, wifi);
            msg.event_type = LED_EVENT_WIFI_STATUS_CHANGED;
            msg.data.wifi_status = wifi;

            if (OSAMsgQSend(s_evt_q, sizeof(led_event_msg_t), (UINT8 *)&msg,
                            OS_NO_SUSPEND) != OS_SUCCESS)
            {
                LED_LOG_ERROR("Failed to send WiFi status event");
            }

            last_wifi = wifi;
        }

        if (charger != last_charger)
        {
            LED_LOG_INFO("Sending charger status event: %d -> %d", last_charger, charger);
            msg.event_type = LED_EVENT_CHARGER_STATUS_CHANGED;
            msg.data.charger_status = charger;

            if (OSAMsgQSend(s_evt_q, sizeof(led_event_msg_t), (UINT8 *)&msg,
                            OS_NO_SUSPEND) != OS_SUCCESS)
            {
                LED_LOG_ERROR("Failed to send charger status event");
            }

            last_charger = charger;
        }

        OSATaskSleep(TASK_SLEEP_POLL_TICK);
    }

    LED_LOG_INFO("Status task exit");
    s_status_task = NULL;
    OSATaskDelete(NULL);
}

/* 清理资源 */
static void cleanup_resources(void)
{
    s_exit_flag = true;

    /* 等待任务自行退出，或超时后强制删除 */
    uint32_t start = OSAGetTicks();
    while ((s_led_task || s_status_task) &&
           ((OSAGetTicks() - start) < TASK_EXIT_TIMEOUT_TICK))
    {
        OSATaskSleep(TASK_SLEEP_MIN_TICK);
    }

    if (s_led_task)
    {
        OSATaskDelete(s_led_task);
        s_led_task = NULL;
    }

    if (s_status_task)
    {
        OSATaskDelete(s_status_task);
        s_status_task = NULL;
    }

    if (s_evt_q)
    {
        OSAMsgQDelete(s_evt_q);
        s_evt_q = NULL;
    }
}

/* 初始化 */
int xy_led_init(void)
{
    int i;

    if (s_evt_q)
    {
        LED_LOG_INFO("Already initialized");
        return 0;
    }

    LED_LOG_INFO("LED module init");
    s_exit_flag = false;

    /* GPIO init */
    if (xy_led_gpio_init() != 0)
    {
        LED_LOG_ERROR("LED GPIO init failed");
        return -1;
    }

    /* 创建队列 */
    if (OSAMsgQCreate(&s_evt_q, LED_MSG_QUEUE_NAME, sizeof(led_event_msg_t),
                      LED_EVENT_QUEUE_SIZE, OS_FIFO) != OS_SUCCESS)
    {
        LED_LOG_ERROR("Create msgq fail");
        goto fail;
    }

    /* 创建任务 */
    if (OSATaskCreate(&s_led_task, s_led_stack, sizeof(s_led_stack),
                      LED_TASK_PRIORITY, LED_TASK_NAME,
                      led_task_entry, NULL) != OS_SUCCESS)
    {
        LED_LOG_ERROR("Create led task fail");
        goto fail;
    }

    if (OSATaskCreate(&s_status_task, s_status_stack, sizeof(s_status_stack),
                      STATUS_TASK_PRIORITY, STATUS_TASK_NAME,
                      led_status_task_entry, NULL) != OS_SUCCESS)
    {
        LED_LOG_ERROR("Create status task fail");
        goto fail;
    }

    LED_LOG_INFO("LED module init ok");
    return 0;

fail:
    cleanup_resources();
    return -1;
}

/* 反初始化 */
int xy_led_deinit(void)
{
    if (!s_evt_q)
    {
        return 0;
    }

    cleanup_resources();
    LED_LOG_INFO("LED module deinit ok");
    return 0;
}
