#ifndef XY_LED_PRIV_H
#define XY_LED_PRIV_H

#ifdef __cplusplus
extern "C" {
#endif

/* ========== 包含文件 ========== */
#include "UART.h"
#include "ql_gpio.h"

/* ========== 宏定义 ========== */
/* 日志输出宏定义 */
#define LED_LOG_INFO(fmt, ...) RTI_LOG("[LED/INFO] " fmt "\r\n", ##__VA_ARGS__)
#define LED_LOG_ERROR(fmt, ...) RTI_LOG("[LED/ERROR] " fmt "\r\n", ##__VA_ARGS__)

/* RTOS任务参数配置 */
#define LED_TASK_STACK_SIZE (1024)          /**< LED控制任务栈大小 */
#define LED_TASK_PRIORITY (26)              /**< LED控制任务优先级 */
#define LED_TASK_NAME "led_ctrl_task"       /**< LED控制任务名称 */
#define STATUS_TASK_STACK_SIZE (2048)       /**< 状态监控任务栈大小 */
#define STATUS_TASK_PRIORITY (24)           /**< 状态监控任务优先级 */
#define STATUS_TASK_NAME "led_status_task"  /**< 状态监控任务名称 */
#define LED_MSG_QUEUE_NAME "led_msg_queue"

/* 事件队列大小 */
#define LED_EVENT_QUEUE_SIZE (10)           /**< 事件队列大小 */

/* 时间转换：ms <-> tick （1 tick = 5ms）*/
#define MS_TO_TICK(ms) (((ms) + 4) / 5)     /**< 毫秒转换为系统tick */

/* 业务相关时间阈值 */
#define BLINK_1HZ_PERIOD_TICK MS_TO_TICK(1000)  /**< 1Hz闪烁周期 (1秒) */
#define TASK_SLEEP_POLL_TICK MS_TO_TICK(300)    /**< 状态轮询间隔 (300ms) */
#define TASK_EXIT_TIMEOUT_TICK MS_TO_TICK(2000) /**< 任务退出超时 (2秒) */
#define TASK_SLEEP_MIN_TICK (1)                 /**< 最小睡眠时间 (1 tick) */

/* 电池电量阈值定义 */
#define LOW_BATTERY_THRESHOLD 20 /**< 低电量阈值百分比 */

/* ========== 枚举定义 ========== */
/**
 * @brief LED显示状态枚举
 */
typedef enum
{
    LED_OFF = 0,        /**< LED关闭 */
    LED_ON,             /**< LED常亮 */
    LED_BLINK_1HZ,      /**< LED以1Hz频率闪烁 (1秒亮，1秒灭) */
} led_status_e;

/**
 * @brief 网络状态枚举
 * @details 根据SIM卡状态、网络注册情况、网络类型和信号强度来区分
 */
typedef enum
{
    NET_STATUS_NO_SIM = 0,           /**< 无SIM卡或PIN码未解锁 */
    NET_STATUS_NO_REG,               /**< 有SIM卡但未注册到网络 */
    NET_STATUS_4G_REG_GOOD,          /**< 4G网络注册且信号强度好 (RSRP≥-110dBm) */
    NET_STATUS_4G_REG_POOR,          /**< 4G网络注册但信号强度差 (RSRP<-110dBm) */
    NET_STATUS_5G_REG_GOOD,          /**< 5G网络注册且信号强度好 (RSRP≥-110dBm) */
    NET_STATUS_5G_REG_POOR,          /**< 5G网络注册但信号强度差 (RSRP<-110dBm) */
} led_net_status_e;

/**
 * @brief WiFi状态枚举
 */
typedef enum
{
    WIFI_STATUS_OFF = 0,    /**< WiFi关闭 */
    WIFI_STATUS_ON,         /**< WiFi开启 */
} led_wifi_status_e;

/**
 * @brief 充电和电池状态枚举
 * @details 根据电池电量和充电状态的组合来区分
 */
typedef enum
{
    CHARGER_STATUS_LOW_BATTERY = 0,        /**< 低电量 (电量<20%) */
    CHARGER_STATUS_LOW_BATTERY_CHARGING,   /**< 低电量充电中 (电量<20%且充电) */
    CHARGER_STATUS_NORMAL_BATTERY,         /**< 正常电量 (电量≥20%或已充满) */
    CHARGER_STATUS_NORMAL_BATTERY_CHARGING /**< 正常电量充电中 (电量≥20%且充电) */
} led_charger_status_e;

/**
 * @brief LED事件类型枚举
 * @details 用于任务间通信的事件类型定义
 */
typedef enum
{
    LED_EVENT_NET_STATUS_CHANGED = 0x01,    /**< 网络状态变化事件 */
    LED_EVENT_WIFI_STATUS_CHANGED = 0x02,   /**< WiFi状态变化事件 */
    LED_EVENT_CHARGER_STATUS_CHANGED = 0x03,/**< 充电状态变化事件 */
} led_event_e;

/**
 * @brief LED索引枚举
 * @details 定义各个LED在配置表中的索引位置
 * @note 索引顺序必须与led_config_t数组的初始化顺序保持一致
 */
typedef enum
{
    LED_IDX_NET_4G_RED = 0,     /**< 4G网络红色指示灯 */
    LED_IDX_NET_4G_GREEN,       /**< 4G网络绿色指示灯 */
    LED_IDX_NET_5G_RED,         /**< 5G网络红色指示灯 */
    LED_IDX_NET_5G_GREEN,       /**< 5G网络绿色指示灯 */
    LED_IDX_WIFI_GREEN,         /**< WiFi绿色指示灯 */
    LED_IDX_CHARGER_RED,        /**< 充电红色指示灯 */
    LED_IDX_CHARGER_GREEN,      /**< 充电绿色指示灯 */
    LED_COUNT                   /**< LED总数量 */
} led_idx_e;

/* ========== 结构体定义 ========== */
/**
 * @brief LED配置结构体
 * @details 包含LED的硬件配置信息和当前状态
 */
typedef struct
{
    ql_gpio_num_e gpio_pin;     /**< GPIO管脚号 */
    const char *name;           /**< LED名称，用于调试和日志输出 */
    led_status_e status;        /**< LED当前显示状态 */
} led_config_t;

/**
 * @brief LED事件消息结构体
 * @details 用于任务间传递状态变化事件的消息格式
 */
typedef struct
{
    led_event_e event_type;     /**< 事件类型 */
    union {
        led_net_status_e net_status;        /**< 网络状态数据 */
        led_wifi_status_e wifi_status;      /**< WiFi状态数据 */
        led_charger_status_e charger_status;/**< 充电状态数据 */
    } data;                     /**< 事件数据联合体 */
} led_event_msg_t;

#ifdef __cplusplus
}
#endif

#endif /* XY_LED_PRIV_H */
